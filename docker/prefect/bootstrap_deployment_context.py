import asyncio
import os

from prefect import flow, get_client

from shared.python.utils.get_aws_config import get_aws_config, get_config
from shared.python.utils.process_resources import (
    get_param_values_from_stack_config,
    get_process_resource_ids,
    get_subnets_by_name_keyword,
)

# === CONFIGURATION ===
WORK_POOL_NAME = "ecs-shared-pool"
DEPLOYMENT_NAME = "task-a-deployment"
FLOW_PATH = "flows/my_flow.py:my_flow"  # Must exist locally
DOCKER_IMAGE = "your-ecr-repo/task-a:latest"
TASK_DEF_ARN = "arn:aws:ecs:region:account-id:task-definition/task-a:3"
ECS_REGION = "us-west-2"
ECS_CLUSTER = "your-ecs-cluster"
ECS_SUBNETS = ["subnet-123abc"]
ECS_SGS = ["sg-123abc"]
ECS_EXEC_ROLE = "arn:aws:iam::account-id:role/ecsTaskExecutionRole"


def create_job_template(
    region: str,
    cluster: str,
    task_definition_arn: str,
    subnets: list[str],
    security_groups: list[str],
    execution_role_arn: str,
) -> dict:
    """
    Create a job template with the provided ECS configuration values.

    Args:
        region: AWS region for ECS tasks
        cluster: ECS cluster name
        task_definition_arn: ARN of the ECS task definition
        subnets: List of subnet IDs
        security_groups: List of security group IDs
        execution_role_arn: ARN of the ECS task execution role

    Returns:
        Job template dictionary with variables filled in
    """
    return {
        "variables": {
            "type": "object",
            "properties": {
                "region": {"type": "string", "default": region},
                "cluster": {"type": "string", "default": cluster},
                "taskDefinitionArn": {"type": "string", "default": task_definition_arn},
                "subnets": {
                    "type": "array",
                    "items": {"type": "string"},
                    "default": subnets,
                },
                "securityGroups": {
                    "type": "array",
                    "items": {"type": "string"},
                    "default": security_groups,
                },
                "executionRoleArn": {"type": "string", "default": execution_role_arn},
            },
            "required": ["cluster", "taskDefinitionArn", "subnets", "region"],
        }
    }


async def ensure_work_pool(client, work_pool_name: str, job_template: dict):
    """
    Ensure the work pool exists, creating it if necessary.

    Args:
        client: Prefect client instance
        work_pool_name: Name of the work pool
        job_template: Job template configuration for the work pool
    """
    try:
        await client.read_work_pool(work_pool_name)
        print(f"✅ Work pool '{work_pool_name}' already exists.")
    except Exception:
        print(f"🛠 Creating work pool '{work_pool_name}'...")
        await client.create_work_pool(
            name=work_pool_name, type="ecs", base_job_template=job_template
        )


async def main():
    # Get configuration
    bootstrap_config_path = os.getenv(
        "BOOTSTRAP_CONFIG_PATH", "docker/prefect/prefect_bootstrap_config.yaml"
    )
    bootstrap_config = get_config(bootstrap_config_path)

    aws_config = get_aws_config()
    aws_region = aws_config.get("aws-region")
    async with get_client() as client:
        for pool in bootstrap_config.get("pools"):
            pool_name = pool.get("pool-name")
            task_process_names = pool.get("task-process-names")

            # Get resources for the process
            relevant_process_resource_ids = get_process_resource_ids(
                aws_config, pool_name
            )

            param_value = get_param_values_from_stack_config(
                aws_config.get("infrastructure", {}).get("stacks", {}),
                relevant_process_resource_ids.get("cluster", [])[0],
            )

            JOB_TEMPLATE = create_job_template(
                region=ECS_REGION,
                cluster=ECS_CLUSTER,
                task_definition_arn=TASK_DEF_ARN,
                subnets=ECS_SUBNETS,
                security_groups=ECS_SGS,
                execution_role_arn=ECS_EXEC_ROLE,
            )
            exit()
            # === Step 1: Ensure Work Pool ===
            await ensure_work_pool(client, WORK_POOL_NAME, JOB_TEMPLATE)

            # === Step 2: Ensure Deployment ===
            existing_deployments = await client.read_deployments(name=DEPLOYMENT_NAME)
            if existing_deployments:
                print(f"✅ Deployment '{DEPLOYMENT_NAME}' already exists.")
            else:
                print(f"🛠 Creating deployment '{DEPLOYMENT_NAME}'...")
                # In Prefect 3.x, we use flow.from_source().deploy() instead of Deployment.build_from_flow()
                # This assumes the flow code is available at the specified entrypoint
                flow_obj = flow.from_source(
                    source=".",  # Current directory as source
                    entrypoint=FLOW_PATH,
                )

                await flow_obj.deploy(
                    name=DEPLOYMENT_NAME,
                    work_pool_name=WORK_POOL_NAME,
                    image=DOCKER_IMAGE,
                    push=False,  # Don't push image to registry in this example
                )
                print(f"✅ Deployment '{DEPLOYMENT_NAME}' registered.")


if __name__ == "__main__":
    asyncio.run(main())
