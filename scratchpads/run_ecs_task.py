import json
import sys

import boto3

from shared.python.utils.get_aws_config import get_aws_config
from shared.python.utils.process_resources import (
    get_param_values_from_stack_config,
    get_subnets_by_name_keyword,
)


def run_ecs_task(
    cluster_name,
    task_definition,
    subnet_ids=None,
    security_groups=None,
    aws_region=None,
):
    """
    Kicks off an ECS task using the specified task definition and cluster

    Parameters:
    - cluster_name: Name of the ECS cluster
    - task_definition: Name or ARN of the task definition
    - subnet_ids: List of subnet IDs for the task (optional)
    - security_groups: List of security group IDs (optional)
    - aws_region: AWS region to connect to (optional)

    Returns:
    - Response from the run_task API call
    """
    # Initialize ECS client
    if aws_region:
        ecs_client = boto3.client("ecs", region_name=aws_region)
    else:
        ecs_client = boto3.client("ecs")

    # Define task parameters
    run_task_params = {
        "cluster": cluster_name,
        "taskDefinition": task_definition,
        "launchType": "FARGATE",  # Assuming Fargate, change if using EC2
    }

    # Add network configuration if subnet IDs are provided
    if subnet_ids:
        network_config = {
            "awsvpcConfiguration": {
                "subnets": subnet_ids,
                "assignPublicIp": "ENABLED",  # Change to DISABLED if not needed
            }
        }

        if security_groups:
            network_config["awsvpcConfiguration"]["securityGroups"] = security_groups

        run_task_params["networkConfiguration"] = network_config

    try:
        # Run the task
        response = ecs_client.run_task(**run_task_params)
        return response
    except Exception as e:
        print(f"Error running ECS task: {e}")
        return None


if __name__ == "__main__":
    aws_config = get_aws_config()
    aws_region = aws_config.get("aws-region")

    extraction_config = (
        aws_config.get("infrastructure", {}).get("stacks", {}).get("extraction", {})
    )

    cluster, task = get_param_values_from_stack_config(
        extraction_config, ["cluster", "tasks-api"]
    )

    network_config = (
        aws_config.get("infrastructure", {}).get("stacks", {}).get("network", {})
    )

    vpc, security_groups = get_param_values_from_stack_config(
        network_config, ["vpc", "ecs-sg"]
    )

    # Get private subnets for Fargate tasks
    subnet_ids = get_subnets_by_name_keyword(vpc, "private")

    if not subnet_ids:
        print("No matching subnets found. Using default subnet IDs.")
        exit(1)

    print(f"Starting ECS task with definition: {task}")
    print(f"On cluster: {cluster}")

    response = run_ecs_task(
        cluster_name=cluster,
        task_definition=task,
        subnet_ids=["subnet-0ac841b68019ea901"],
        # subnet_ids=subnet_ids,
        security_groups=[security_groups],
        aws_region=aws_region,
    )

    if response:
        print("Task started successfully:")
        print(json.dumps(response, indent=2, default=str))

        # Extract and display task ARN
        if "tasks" in response and response["tasks"]:
            task_arn = response["tasks"][0]["taskArn"]
            print(f"\nTask ARN: {task_arn}")
        else:
            print("\nNo tasks were started.")
    else:
        print("Failed to start task.")
        sys.exit(1)
